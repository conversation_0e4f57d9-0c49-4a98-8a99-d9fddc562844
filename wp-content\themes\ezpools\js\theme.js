/**
 * EZ Pools Theme JavaScript - Modern Enhanced Version
 */

(function($) {
    'use strict';

    $(document).ready(function() {

        // Add loading class to body
        $('body').addClass('loading');

        // Remove loading class when everything is loaded
        $(window).on('load', function() {
            setTimeout(function() {
                $('body').removeClass('loading').addClass('loaded');
            }, 500);
        });

        // Initialize modern features
        initModernFeatures();

        // Mobile menu toggle with enhanced animation
        $('.mobile-menu-toggle').on('click', function() {
            $('.main-nav').toggleClass('active');
            $(this).toggleClass('active');
            $('body').toggleClass('menu-open');
        });

        // Enhanced smooth scrolling for anchor links
        $('a[href^="#"]').on('click', function(e) {
            e.preventDefault();

            var target = $(this.getAttribute('href'));
            if (target.length) {
                var headerHeight = $('.site-header').outerHeight();
                $('html, body').stop().animate({
                    scrollTop: target.offset().top - headerHeight - 20
                }, 1200, 'easeInOutCubic');
            }
        });

        // Enhanced header scroll effect with parallax
        $(window).on('scroll', function() {
            var scroll = $(window).scrollTop();
            var windowHeight = $(window).height();

            // Header scroll effect
            if (scroll >= 50) {
                $('.site-header').addClass('scrolled');
            } else {
                $('.site-header').removeClass('scrolled');
            }

            // Parallax effect for hero section
            if (scroll < windowHeight) {
                $('.hero').css('transform', 'translateY(' + (scroll * 0.5) + 'px)');
            }
        });

        // Advanced scroll animations with Intersection Observer
        function initScrollAnimations() {
            if ('IntersectionObserver' in window) {
                const animationObserver = new IntersectionObserver((entries) => {
                    entries.forEach(entry => {
                        if (entry.isIntersecting) {
                            entry.target.classList.add('animate-in');
                            // Add staggered animation for grid items
                            if (entry.target.classList.contains('feature-card') ||
                                entry.target.classList.contains('product-card')) {
                                const siblings = Array.from(entry.target.parentNode.children);
                                const index = siblings.indexOf(entry.target);
                                entry.target.style.animationDelay = `${index * 0.1}s`;
                            }
                        }
                    });
                }, {
                    threshold: 0.1,
                    rootMargin: '0px 0px -50px 0px'
                });

                // Observe elements for animation
                document.querySelectorAll('.feature-card, .product-card, .testimonial-slide, .stat').forEach(el => {
                    animationObserver.observe(el);
                });
            }
        }

        // Initialize scroll animations
        initScrollAnimations();

        // Modern contact form handling with enhanced UX
        $('.contact-form').on('submit', function(e) {
            e.preventDefault();

            var form = $(this);
            var submitBtn = form.find('.submit-btn');
            var originalText = submitBtn.text();

            // Add loading state with animation
            submitBtn.prop('disabled', true)
                     .html('<span class="loading-spinner"></span> Sending...')
                     .addClass('loading');

            // Simulate form submission with enhanced feedback
            setTimeout(function() {
                submitBtn.prop('disabled', false)
                         .text('✓ Message Sent!')
                         .removeClass('loading')
                         .addClass('success');

                $('.form-success').fadeIn().addClass('animate-in');
                form[0].reset();

                // Reset button after delay
                setTimeout(function() {
                    submitBtn.text(originalText).removeClass('success');
                }, 3000);
            }, 2000);
        });

        // Initialize modern features
        function initModernFeatures() {
            // Add loading animations to images
            $('img').each(function() {
                $(this).on('load', function() {
                    $(this).addClass('loaded');
                });
            });

            // Add hover effects to interactive elements
            $('.cta-button, .feature-card, .product-card').hover(
                function() {
                    $(this).addClass('hover-active');
                },
                function() {
                    $(this).removeClass('hover-active');
                }
            );

            // Initialize particle background for hero section
            if ($('.hero').length) {
                createParticleBackground();
            }

            // Add smooth reveal animations for stats
            animateStats();
        }

        // Create subtle particle background effect
        function createParticleBackground() {
            const hero = document.querySelector('.hero');
            if (!hero) return;

            for (let i = 0; i < 20; i++) {
                const particle = document.createElement('div');
                particle.className = 'particle';
                particle.style.cssText = `
                    position: absolute;
                    width: ${Math.random() * 4 + 2}px;
                    height: ${Math.random() * 4 + 2}px;
                    background: rgba(255, 255, 255, ${Math.random() * 0.3 + 0.1});
                    border-radius: 50%;
                    left: ${Math.random() * 100}%;
                    top: ${Math.random() * 100}%;
                    animation: float ${Math.random() * 10 + 10}s infinite linear;
                    pointer-events: none;
                `;
                hero.appendChild(particle);
            }
        }

        // Animate statistics with counting effect
        function animateStats() {
            $('.stat-number').each(function() {
                const $this = $(this);
                const text = $this.text();
                const hasPlus = text.includes('+');
                const hasLess = text.includes('<');
                const number = parseInt(text.replace(/[^\d]/g, ''));

                if (!isNaN(number)) {
                    $this.text('0' + (hasPlus ? '+' : '') + (hasLess ? '<' : ''));

                    const observer = new IntersectionObserver((entries) => {
                        entries.forEach(entry => {
                            if (entry.isIntersecting) {
                                animateNumber($this, number, hasPlus, hasLess);
                                observer.unobserve(entry.target);
                            }
                        });
                    });

                    observer.observe($this[0]);
                }
            });
        }

        // Number counting animation
        function animateNumber($element, target, hasPlus, hasLess) {
            let current = 0;
            const increment = target / 50;
            const timer = setInterval(() => {
                current += increment;
                if (current >= target) {
                    current = target;
                    clearInterval(timer);
                }

                let displayText = Math.floor(current).toString();
                if (hasLess) displayText = '<' + displayText;
                if (hasPlus) displayText += '+';
                if (target === 10) displayText = '1/10'; // Special case for fraction

                $element.text(displayText);
            }, 50);
        }

        // Product filter functionality
        $('.product-filter').on('click', function(e) {
            e.preventDefault();
            
            var filter = $(this).data('filter');
            
            // Update active filter
            $('.product-filter').removeClass('active');
            $(this).addClass('active');
            
            // Filter products
            if (filter === 'all') {
                $('.product-card').show();
            } else {
                $('.product-card').hide();
                $('.product-card[data-category="' + filter + '"]').show();
            }
        });

        // Image lazy loading
        if ('IntersectionObserver' in window) {
            const imageObserver = new IntersectionObserver((entries, observer) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const img = entry.target;
                        img.src = img.dataset.src;
                        img.classList.remove('lazy');
                        imageObserver.unobserve(img);
                    }
                });
            });

            document.querySelectorAll('img[data-src]').forEach(img => {
                imageObserver.observe(img);
            });
        }

        // Testimonial slider
        let currentTestimonial = 0;
        const testimonials = $('.testimonial-slide');
        const totalTestimonials = testimonials.length;

        function showTestimonial(index) {
            testimonials.removeClass('active');
            testimonials.eq(index).addClass('active');
            
            $('.testimonial-dot').removeClass('active');
            $('.testimonial-dot').eq(index).addClass('active');
        }

        function nextTestimonial() {
            currentTestimonial = (currentTestimonial + 1) % totalTestimonials;
            showTestimonial(currentTestimonial);
        }

        function prevTestimonial() {
            currentTestimonial = (currentTestimonial - 1 + totalTestimonials) % totalTestimonials;
            showTestimonial(currentTestimonial);
        }

        // Auto-advance testimonials
        if (testimonials.length > 1) {
            setInterval(nextTestimonial, 5000);
        }

        // Testimonial navigation
        $('.testimonial-next').on('click', nextTestimonial);
        $('.testimonial-prev').on('click', prevTestimonial);
        
        $('.testimonial-dot').on('click', function() {
            currentTestimonial = $(this).index();
            showTestimonial(currentTestimonial);
        });

        // Pool size calculator
        $('.size-calculator').on('submit', function(e) {
            e.preventDefault();
            
            var width = parseFloat($('#calc-width').val());
            var length = parseFloat($('#calc-length').val());
            var depth = parseFloat($('#calc-depth').val()) || 4;
            
            if (width && length) {
                var gallons = Math.round(width * length * depth * 7.48);
                var estimatedPrice = Math.round(gallons * 0.15); // Rough estimate
                
                $('.calculator-result').html(
                    '<h4>Pool Estimate</h4>' +
                    '<p><strong>Dimensions:</strong> ' + width + '\' × ' + length + '\' × ' + depth + '\'</p>' +
                    '<p><strong>Capacity:</strong> ' + gallons.toLocaleString() + ' gallons</p>' +
                    '<p><strong>Estimated Price:</strong> $' + estimatedPrice.toLocaleString() + '</p>' +
                    '<p><em>Contact us for an exact quote</em></p>'
                ).show();
            }
        });

        // FAQ accordion
        $('.faq-question').on('click', function() {
            var faqItem = $(this).parent();
            var answer = faqItem.find('.faq-answer');
            
            if (faqItem.hasClass('active')) {
                faqItem.removeClass('active');
                answer.slideUp();
            } else {
                $('.faq-item').removeClass('active');
                $('.faq-answer').slideUp();
                faqItem.addClass('active');
                answer.slideDown();
            }
        });

        // Newsletter signup
        $('.newsletter-form').on('submit', function(e) {
            e.preventDefault();
            
            var email = $(this).find('input[type="email"]').val();
            var form = $(this);
            
            if (email) {
                form.find('.submit-btn').prop('disabled', true).text('Subscribing...');
                
                // Simulate subscription (replace with actual AJAX call)
                setTimeout(function() {
                    form.find('.submit-btn').prop('disabled', false).text('Subscribe');
                    $('.newsletter-success').show();
                    form[0].reset();
                }, 1500);
            }
        });

        // Back to top button
        $(window).on('scroll', function() {
            if ($(this).scrollTop() > 500) {
                $('.back-to-top').fadeIn();
            } else {
                $('.back-to-top').fadeOut();
            }
        });

        $('.back-to-top').on('click', function(e) {
            e.preventDefault();
            $('html, body').animate({scrollTop: 0}, 1000);
        });

    });

})(jQuery);
