services:
  # WordPress service
  wordpress:
    image: wordpress:6.4-php8.1-apache
    container_name: ezpools-wordpress
    restart: unless-stopped
    ports:
      - "8080:80"
    environment:
      WORDPRESS_DB_HOST: db:3306
      WORDPRESS_DB_USER: ezpools_user
      WORDPRESS_DB_PASSWORD: ezpools_password
      WORDPRESS_DB_NAME: ezpools_wp
      WORDPRESS_TABLE_PREFIX: wp_
      WORDPRESS_DEBUG: 1
    volumes:
      - wordpress_data:/var/www/html
      - ./wp-content:/var/www/html/wp-content:rw
    depends_on:
      - db
    networks:
      - ezpools-network

  # Setup service to configure WordPress after it's running
  setup:
    image: wordpress:cli-php8.1
    container_name: ezpools-setup
    volumes:
      - wordpress_data:/var/www/html
      - ./wp-content:/var/www/html/wp-content:rw
      - ./auto-setup.sh:/auto-setup.sh:ro
    environment:
      WORDPRESS_DB_HOST: db:3306
      WORDPRESS_DB_USER: ezpools_user
      WORDPRESS_DB_PASSWORD: ezpools_password
      WORDPRESS_DB_NAME: ezpools_wp
    depends_on:
      - wordpress
      - db
    networks:
      - ezpools-network
    command: >
      sh -c "
        echo 'Waiting for WordPress to be ready...'
        while ! curl -s http://wordpress > /dev/null; do
          sleep 2
        done
        echo 'WordPress is ready! Running setup...'
        chmod +x /auto-setup.sh
        /auto-setup.sh
        echo 'Setup complete!'
      "

  # MySQL database service
  db:
    image: mysql:8.0
    container_name: ezpools-mysql
    restart: unless-stopped
    environment:
      MYSQL_DATABASE: ezpools_wp
      MYSQL_USER: ezpools_user
      MYSQL_PASSWORD: ezpools_password
      MYSQL_ROOT_PASSWORD: root_password
      MYSQL_CHARSET: utf8mb4
      MYSQL_COLLATION: utf8mb4_unicode_ci
    volumes:
      - db_data:/var/lib/mysql
      - ./setup-database.sql:/docker-entrypoint-initdb.d/setup-database.sql
    ports:
      - "3306:3306"
    networks:
      - ezpools-network

  # phpMyAdmin for database management (optional)
  phpmyadmin:
    image: phpmyadmin/phpmyadmin:latest
    container_name: ezpools-phpmyadmin
    restart: unless-stopped
    ports:
      - "8081:80"
    environment:
      PMA_HOST: db
      PMA_PORT: 3306
      PMA_USER: ezpools_user
      PMA_PASSWORD: ezpools_password
      MYSQL_ROOT_PASSWORD: root_password
    depends_on:
      - db
    networks:
      - ezpools-network

  # WordPress CLI for management tasks
  wp-cli:
    image: wordpress:cli-php8.1
    container_name: ezpools-wp-cli
    volumes:
      - wordpress_data:/var/www/html
      - ./wp-content:/var/www/html/wp-content:rw
    environment:
      WORDPRESS_DB_HOST: db:3306
      WORDPRESS_DB_USER: ezpools_user
      WORDPRESS_DB_PASSWORD: ezpools_password
      WORDPRESS_DB_NAME: ezpools_wp
    depends_on:
      - db
      - wordpress
    networks:
      - ezpools-network

volumes:
  wordpress_data:
  db_data:

networks:
  ezpools-network:
    driver: bridge
